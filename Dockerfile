FROM --platform=linux/amd64 python:3.13-slim-bookworm as builder
# Force build on AMD platform to replicate the behaviour from Gitlab on Mac M1 machines

# 🚀 Optimized for size and build speed using techniques from 
# https://indiebi.atlassian.net/wiki/spaces/DPT/pages/362512422/Optimizing+Dockerfiles+for+Python

WORKDIR /app

RUN apt-get update && apt-get install -y curl gnupg \
    && curl -fsSL https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor -o /usr/share/keyrings/microsoft-prod.gpg \
    && curl https://packages.microsoft.com/config/debian/12/prod.list | tee /etc/apt/sources.list.d/mssql-release.list \
    && apt-get update && ACCEPT_EULA=Y apt-get install -y msodbcsql18 mssql-tools18 msodbcsql17 mssql-tools unixodbc-dev


RUN pip install poetry && \
    poetry config virtualenvs.in-project true

COPY poetry.lock pyproject.toml /app/
# removing unneeded directories here is more about speeding up Kaniko build than reducing target image size
RUN poetry install --only main,test --no-interaction --no-root --no-cache && \
    rm -rf /root/.local/ && \
    rm -rf /root/.cache/

FROM --platform=linux/amd64 python:3.13-slim-bookworm


COPY --from=builder /etc/apt/sources.list.d/mssql-release.list /etc/apt/sources.list.d/mssql-release.list
COPY --from=builder /usr/share/keyrings/microsoft-prod.gpg /usr/share/keyrings/microsoft-prod.gpg

RUN apt-get update && \
    ACCEPT_EULA=Y apt-get install --no-install-recommends -y unixodbc msodbcsql17 msodbcsql18 mssql-tools mssql-tools18 unixodbc-dev && \
    rm -rf /var/lib/apt/lists/* && \
    echo "/opt/microsoft/msodbcsql17/lib64" >> /etc/ld.so.conf.d/mssql.conf && \
    echo "/opt/microsoft/msodbcsql18/lib64" >> /etc/ld.so.conf.d/mssql.conf && \
    ldconfig && \
    groupadd --gid 1000 service && useradd --uid 1000 --gid 1000 -r -g service service


COPY --from=builder --chown=service:service /app/.venv /app/.venv
COPY --chown=service:service alembic /app/alembic
COPY --chown=service:service alembic.ini /app/alembic.ini
COPY --chown=service:service dataset_manager /app/dataset_manager
COPY --chown=service:service static /app/static
COPY --chown=service:service test /app/test

USER service

ARG DOCKER_TAG
ENV DOCKER_TAG=$DOCKER_TAG
ENV SENTRY_RELEASE=$DOCKER_TAG

ARG DOCKER_BUILD_TIMESTAMP
ENV DOCKER_BUILD_TIMESTAMP=$DOCKER_BUILD_TIMESTAMP
ENV PATH="/app/.venv/bin:${PATH}"

WORKDIR /app


CMD ["uvicorn", "dataset_manager.main:app", "--host", "0.0.0.0", "--port", "8000"]