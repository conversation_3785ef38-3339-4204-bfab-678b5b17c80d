[tool.poetry]
name = "dataset_manager"
version = "0.1.0"
description = ""
authors = ["Data Team"]

[tool.ruff]
src = [".", "tests"]

[tool.ruff.lint]
select = [
    # Pyflakes
    "F",
    # Pycodestyle
    "E",
    "W",
    # isort
    "I",
]
ignore = ["E501", "E712"]

[tool.ruff.lint.per-file-ignores]
"alembic/env.py" = ["F401"]
"dataset_manager/main.py" = ["E402"]
"dataset_manager/api/studio/__init__.py" = ["F401"]
"dataset_manager/api/dataset/__init__.py" = ["F401"]


[tool.ruff.lint.isort]
known-first-party = ["dataset_manager"]
known-third-party = ["alembic"]

[tool.mypy]
exclude = ["alembic", "scripts"]
plugins = ["sqlalchemy.ext.mypy.plugin"]

[[tool.mypy.overrides]]
module = ["opencensus.*", "requests_toolbelt", "pyodbc", "factory"]
ignore_missing_imports = "true"


[tool.poetry.dependencies]
python = ">=3.13, <3.14"
azure-common = "^1.1.27"
azure-core = "1.31.0"
azure-identity = "1.19.0"
azure-storage-blob = "12.23.1"
fire = { extras = ["all"], version = "^0.7.0" }
logging-json = "^0.6.0"
pydantic = "^2.6.3"
types-python-dateutil = "^2.8.9"
freezegun = "^1.2.1"                                      # move to test
requests = "^2.27.1"
requests-toolbelt = "^1.0.0"
fastapi = { extras = ["all"], version = "^0.115.0" }
uvicorn = "^0.35.0"
alembic = "1.16.3"
pyodbc = "5.2.0"
SQLAlchemy = { version = "1.4.54", extras = ["mypy"] }
psutil = "^6.0.0"
python-multipart = "^0.0.20"
sentry-sdk = { extras = ["fastapi"], version = "^2.0.0" }
ecs-logging = "^2.1.0"
elastic-apm = "^6.21.3"
httpx = "^0.27.0"
aiohttp = "^3.9.5"
pydantic-settings = "^2.4.0"

[tool.poetry.group.test.dependencies]
pytest = "^8.0.0"
factory-boy = "^3.3.0"
responses = "^0.25.0"
respx = "^0.22.0"

[tool.poetry.group.dev.dependencies]
mypy = "^1.4.1"
isort = "^5.10.1"
ruff = "^0.12.0"
types-requests = "^********"
types-psutil = "^********"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
